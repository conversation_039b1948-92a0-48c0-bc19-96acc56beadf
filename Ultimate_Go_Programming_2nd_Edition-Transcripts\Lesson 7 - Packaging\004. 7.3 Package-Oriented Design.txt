All right, let's talk about a project structure that I use on all of my projects. Ardan Labs uses it on their projects as well, and I try to get my clients to adopt it too. This isn't the only project structure you can use—I've seen some very nice ones that differ from this—but anytime I see a project structure that's working, trust me, it's applying these design philosophies. The key is being able to validate where code goes, how it should behave, and how it's designed. That’s the big idea here.

I really believe that every company, or at least every team, should have a kit project. I also believe a project should be bound to a single repository—say, in GitHub or whatever source control system you're using. So, you should have a kit project. To me, a kit project is the set of foundational packages or APIs that every application you build should use. I’d want to see things like logging packages (if you're not using the standard library), configuration packages (if you're not using third-party tools), web frameworks you're building—whatever foundational components you rely on. I like to see these laid out at the root of the source tree, right there on the edge. I don’t want to see packages nested inside other packages. I also want these foundational packages to be as decoupled as possible. For example, I don’t want the log package importing config, or config importing log. In fact, these foundational packages probably shouldn’t be doing any logging at all, because logging introduces policy. I’m also not a fan of the logging interface—I think that’s a big mistake. The standard library doesn’t log, and neither should these foundational packages.

Now, if a foundational package has goroutines or execution paths where events occur that you want to log, I think you should ask for a handler function. The user can implement whatever they want in that handler, and you just call it during those events. The point is, we don’t always need interfaces. Asking for a function—like a handler—can streamline things and still give you the logging you need.

That’s what "kit" is: foundational stuff, in its own repo—ideally named "kit." Now, every project you work on beyond that is what I call an application project. An application project can have multiple binaries—it’s not one-to-one. In fact, less is more. And a large team can work within a single application project if package-oriented design is implemented properly. I’ve worked on teams of three to five people using this structure, and we’ve been very successful without stepping on each other.

There are four folders in my application project: "cmd/", "internal/", "internal/platform/", and "vendor/". Each has a very specific purpose, which helps us determine where packages should go. The "vendor" folder is where we handle dependencies using dep (D-E-P). Today, dep is the recommended tool for reproducibility and vendoring. On a new project, you run "dep init", then "dep ensure". Dep will scan all third-party packages—including your kit—and maintain a copy of all source code and versions. You need this. Own all your source code. Unless you're working on something as massive as Kubernetes, you should be able to own all your dependencies. Use dep.

As of now, the Go team is experimenting with VGO—a new front-end tool for Go that will help with vendoring and versioning. We’re not there yet. Maybe a year from now, when you're watching this, we’ll all be using the Go front-end instead of dep. I don’t know how that will shake out, but for today, if you're vendoring, you should be using dep. And please—own, own, own your dependencies if you want high levels of reproducibility in your builds.

That leaves us with "cmd", "internal", and "platform"—each with a special purpose. Let’s start with "cmd". This is where the binaries or applications themselves are built. Let me show you the service project as an example. You’ll see it uses my package-oriented design structure: "cmd", "internal", "vendor", and "platform". Great.

"cmd" is where the applications live—the binaries we’re building. It can be one or many. In this service project, which implements a CRUD-based web service, you’ll see a folder called "crud" with a "main.go" file. That’s the entry point. The binary will be named "crud". If we have multiple binaries, they go under "cmd". In this case, since I’m using a sidecar architecture, you’ll also see two other binaries: "metrics" and "tracer". These are microservices that handle tracing and metrics for the main service. So we have three different binaries, each with their own "main.go".

Packages defined inside a "cmd" subfolder—like "crud"—are very application-specific. They support startup, shutdown, and maybe some light routing in a web service. But not much business logic—more like presentational logic: taking a request, returning a response. The nice thing at the application layer—right at the "cmd" level—is that you can include packages if needed. You can get away with having packages here because they have low reusability. They’re specific to this app and nothing else. You can also have test folders or integration test packages here. That works well. So at the application layer, you have a lot of freedom to set policy. You can do containment, like with handlers, and build multiple binaries.

The "internal" folder is where we put our business logic: business packages, service-level packages, anything that needs to be reusable across multiple binaries. You’ll see here a package called "user" that implements all user CRUD operations. I use files to organize source code, not folders—so I have a file called "models" for the user models. You’ll see the tests are in the same folder, and I have the API for user. You’ll also see a package called "mid" for middleware—this provides business-layer middleware. Any other business logic goes here.

Then there’s "platform", which is for foundational packages that aren’t in "kit". This includes special database support, Docker support, flag handling, specialized testing, tracing, web framework support—anything foundational. So to summarize: "internal/platform" is foundational, "internal" is business logic, and "cmd" is application logic. See how these three layers are separated? I don’t care how you do it, but you really want to separate application, business, and foundational logic—and this is how we do it.

Because I know where packages belong, I can do a lot of validation during code reviews to ensure what we’re doing makes sense. Let me share some of that with you. I’ve already told you we understand where a package goes. If you tell me, "Bill, I need a package to do this," my first question is: What does this package provide? Is it clear and reasonable that it provides just this one thing? We want packages to do one thing, and do it well. Remember our discussion about layered API design—primitive, lower-level layers? I want our packages to be as primitive as possible. Eventually, you’ll have some lower-level and some higher-level packages, so we need to understand what each one provides.

If you tell me it’s business logic, it goes under "internal". If it’s foundational support, it goes under "platform". If it’s very specific to the application—like presentational logic, request/response handling—it goes under "cmd" for that binary. Now we’re not guessing. We’re not saying, "Where does this go today?" and changing our minds tomorrow. And we use the name "internal" because it gives us an extra layer of compiler protection: the Go compiler will prevent any external project from importing packages inside "internal". That’s a great safeguard.

So now I can validate where a package belongs based on its purpose—no ambiguity. We don’t argue about placement. Purpose dictates whether it goes in "platform", "internal", or "cmd". But there’s more we can validate—not just location. We can also examine dependency choices. Anytime a package imports something outside the standard library—whether from "vendor" or elsewhere in the project—things get interesting. Regardless of where the package is, we always need to understand the cost and benefit of that coupling. There’s a real cost to dependencies.

You should never import a package just because you need its types. If I see you importing "models", I’m going to be upset. Why can’t your package have its own types? Why create that binding? I also want to minimize packages at the same level importing each other—especially within "platform" and "internal". It would be ideal if, for example, "database" didn’t import anything else in "platform", or "web" didn’t. You can’t always avoid it, but I try to keep one level deep in both "platform" and "user". It keeps things clearer.

There are times when a small API change can significantly reduce coupling between same-level packages in "platform" or "internal". "Platform" is harder to decouple at the same level, but I work hard to do it in "internal". I really want to avoid same-level imports in "internal". If there is an import relationship, I prefer to use hierarchy—maybe even define the package inside the one that needs it. You can’t always do that, but spend time minimizing same-level dependencies in "internal" and "platform".

Another thing we can validate is the import graph. I’ve structured this project so we can always verify the following: any package in "cmd" can import from "internal" and "platform". You can always import "down", never "up". If you design a different structure, try to maintain this rule: down is good, up is bad. That means a package like "user" should never import anything from "cmd"—that’s a violation. Import down, never up.

So "internal" packages can import from "platform" and "vendor" (down), but never up into "cmd". "Platform" packages can import from "vendor" or other "platform" packages, but they cannot import into "internal" or "cmd". Look at what this structure gives us: we can validate that all imports go downward. Application packages can use business and platform packages, but platform packages can’t import into application code. Imagine how bad that would be.

Policy creates coupling, so here’s another rule: kit and platform packages are not allowed to set policy. They can’t log, they can’t trace. If those capabilities are needed, they must be decoupled. Configuration, metrics, telemetry—all must be decoupled. We don’t want any policy dependencies in kit or platform. But command and internal packages—those are part of the application—they *can* set policy. They have the highest level of freedom because they’re unique to the application.

We’ve also talked about how data is accepted and returned. We want consistent use of value and pointer semantics. We only use interfaces to accept concrete data when we need the behavior—when we need decoupling. If we don’t need decoupling, we avoid the interface cost and ask for the concrete type directly. We should be reasonable about creating new types—if one already exists, consider using it—but we shouldn’t import a whole package just for a type. I’d rather declare a new type to keep a package’s API as decoupled as possible.

We can now look at API parameters and validate how decoupled the API is from other packages. We can do the same with error handling. We’ve said that error handling means an error is logged and the application validates its integrity. If it can’t, it shuts down. Once that’s done, the error is not reported again.

So if I see a kit package calling panic or wrapping errors, I stop the code review. Wrapping errors is a policy decision, and kit packages aren’t allowed to panic the application. Only the application itself can do that. Command packages—our application packages—are allowed to panic and wrap errors. That’s acceptable policy. In fact, most error handling happens at the application or command level.

Internal packages—our business logic—aren’t allowed to panic either. They don’t have that right. But they *can* wrap errors; that level of policy is acceptable. They should also handle errors appropriately. Platform packages are like kit: no panicking, no wrapping—only root cause errors.

See how, once I know a package’s location and purpose, I know exactly what it’s allowed and not allowed to do?

Testing is also important. Test files should always be in the same folder as the code for kit, internal, and platform packages. But at the command level, where we do more integration testing, I don’t mind having a "test" folder. This code isn’t meant to be reusable anyway.

Recovering from panics is another critical area. You must be careful—losing stack trace information can be costly. Application-level packages in "cmd" can recover from panics, but they must ensure integrity is restored. I get nervous when kit, internal, or platform packages recover from panics. There’s one exception: if a package creates and owns its own goroutines, it’s responsible for recovering panics on those goroutines—*if* it can guarantee integrity.

This is why project structure is so important. It stops things from being random. If you tell me you need a package, I’ll ask for its purpose. Once you tell me, I know whether it belongs in "cmd", "internal", "platform", or if it’s part of "kit" and should be in "vendor". I know where it goes. And once I know its location, I know how to validate its imports, policy decisions, and everything around it. We can ensure strong engineering decisions throughout—no arguments.

Package-oriented design allows developers to identify where a package belongs and what design guidelines it must follow—everything we’ve discussed. It defines what a project is and how it’s structured. I’ve shown you one structure that works incredibly well for me, the developers at Ardan Labs, and our clients. It’s not the only one that could work—the end goal is two things: first, to develop clear mental models of our codebase so we know where everything is; and second, to improve team communication so we can promote clean package design, clean architecture, and have valuable discussions instead of arguments.

If I go back to that service code, it’s a great example of package-oriented design in action. I call this "starter kit" code. If you want to build a service and follow along with this training, go find the Ardan Labs / service repository. You’ll see package-oriented design fully implemented, and you’ll also see how to build web services with all the mechanics, semantics, and practices I’ve been teaching since we started this video.