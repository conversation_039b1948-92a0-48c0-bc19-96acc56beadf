So I want to show you one more time how the semantics really drive our ability to read and understand the impact code is going to have. When we are decoupling in Go, it's going to come with the cost of indirection and allocation. And this is not unique to <PERSON>, by the way—decoupling is inherently an allocation-type operation. But let's take a look at this code here. We can really see this, and again, begin to understand that when we understand our mechanics and then understand our semantics, we understand how code is going to behave and what impact it will have, allowing us to make better engineering choices.

Here is the printer interface with the active behavior "print." And here's our concrete type, user. It has a name and implements the printer interface using value semantics. Now, what do we know when we're implementing interfaces with value semantics? We can use both value semantics and pointer semantics, but we prefer to use value semantics to keep our semantics clean. Value semantics are at play with this concrete data, user.

Now I create a user value—there it is, U, with "Bill" in it. Beautiful. That's my concrete data. That's our work. Then on line 32, I do something interesting: I create a slice, a collection, of printer interface values. I now have a collection of data that is not bound to just one concrete type, but can hold any value or pointer that implements the printer interface—the behavior of print. I'm using a slice literal here for construction.

On line 35, I use value semantics to store the first piece of concrete data. This will be a user value, and it will have its own copy of U. Value semantics are in play, which means what? Allocation—there it is. We have a copy. That's our allocation.

Then on line 38, what have we done? We're no longer using value semantics—we're using pointer semantics. That means the interface will hold a *user, meaning it references the original U. Pointer semantics. But again, anytime we store a value inside an interface, we're going to have allocation. Guess what? Allocation—right there. That's our cost: indirection and allocation. Boom, there it is.

But when I go ahead on line 42 and change the data, only index one—not index zero—will see the change. Why? Because index zero is using value semantics (a copy), and index one is using pointer semantics (a reference to the original). We can see and understand the impact we're having.

Now on line 46, we're using the value semantic form of the for range. This is not an accident, not random. Think about it: we're ranging over a collection of interface values. Interfaces are reference types. Reference types use value semantics. So what form of the for range do we use? Value semantics. Do you see how everything is driven by the data, and the data is semantic?

What is the collection? Interfaces. Interfaces use value semantics. So we use value semantics—right there: for _, e := range. Now, e is an interface value local to the for loop.

On the first iteration, what do we get? A copy of index zero. We're using value semantics, which means e is a copy. So now I have two interface values. Pointers are for sharing—efficiency. I'm hoping by now you see the efficiencies we're getting through pointers. But we made a copy—value semantics—so now I have two interface values sharing the same user value.

Then we call print. We call print against e, which calls print against the copy. That's the behavior we get.

On the next iteration, what happens? We get a copy of the next interface value. So we're no longer pointing to the first one. This one holds a *user. We come here, and now when we call print against the same variable e, polymorphism kicks in—the behavior changes depending on the concrete data we're operating on. Now we call print against the original U. And we see the change.

Polymorphism is all about decoupling through behavior. But everything is still driven through the concrete type, and the concrete data having the behavior. Polymorphism means that a piece of code changes its behavior depending on the concrete data it's operating on.

Here, we operated on two different user values—one in value semantic mode, one in pointer semantic mode—and we saw different behavior. This is really important.

And if we maintain these semantics and semantic consistencies throughout our codebase, life is just going to be better—for us, for everyone on the team, and for everyone we haven't even met yet who will eventually have to maintain this code.