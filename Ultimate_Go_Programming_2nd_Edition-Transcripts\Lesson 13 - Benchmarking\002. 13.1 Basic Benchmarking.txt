Benchmarking in Go is incredibly powerful. I'm going to show you some aspects of basic benchmarking now, and then when we get into profiling, I'll show you more. But let's start with a basic benchmark here—let's look at the mechanics and see how we can write these and run a couple right from the command line.

What I'm starting with is on line 17—actually, look at lines 17 and 28. The idea of this benchmark is to do a CPU profile of how fast the Sprintf or Sprint function runs. One important thing about benchmarking is that we must not guess—we have to ensure the benchmark we're writing is accurate. One concern I have is that since this code is compiled, and the compiler is capable of eliminating dead code, particularly with the SSA backend, it can identify scenarios like this: you call a function like Sprint, which doesn't mutate state—it uses value semantics, gets its own copy of the string, and returns a new string. If you don't capture the returned string from Sprint or Sprintf, the compiler can technically determine that the function call is unnecessary and eliminate it entirely. Suddenly, you're trying to benchmark which is faster—Sprint or Sprintf—and both appear blazing fast, not because they are, but because the compiler optimized the calls away.

So in this test, we're ensuring the code isn't being eliminated and that we're using it in a way that mirrors production as accurately as possible. Here's the test: I want to know what's faster—Sprint or Sprintf—for the string "hello". My guess is that Sprint will be faster because we're not doing any formatting. And remember, any time you guess about performance, you're likely to be wrong—but this one seems obvious, right? I'm just demonstrating a basic test: Sprint versus Sprintf.

Now, look at the structure of the test. We have a function named Benchmark—it takes a pointer to *testing.B, not *testing.T. The function name starts with "Benchmark", follows the same export rules, and "Sprint" becomes part of the test name. The key element here is the loop. The way it works is that the first time the testing framework calls this Benchmark function, b.N is set to one. Then, the framework increases b.N over time. The goal is to run enough iterations to feel confident in the results. By default, the test runs for about one second, but we'll increase it to three seconds to ensure sufficient iterations. You need to determine what "enough" iterations are—a million, a hundred million—it's up to you. You can increase the benchmark time to get more iterations and greater confidence, but of course, it will take longer to run.

So in this Benchmark function for Sprint, we have a loop based on b.N. We call Sprint and assign the returned string to a local variable s. We know that if we don't use the local variable, Go's compiler will complain. But to prevent the compiler from optimizing away the function call, we assign the result to a global variable, gs. That's the only purpose of gs—to ensure the compiler doesn't discard the result. This doesn't add extra cost to the benchmark because the real cost we're measuring is inside the loop.

We have benchmarks for both Sprint and Sprintf. Now, make a guess—which one do you think is faster? I guessed Sprint because there's no formatting involved. This code is in an underscore test file. If I go back to the terminal, you can see the file: basic_test.go. We'll use `go test` as before. But since there are no functions starting with "Test", I don't want the tool wasting time looking for them. And if there were any, I definitely wouldn't want them to run—by default, `go test` runs all test functions. So if I only want to run benchmarks, I need to provide a regular expression that won't match any test functions. That's why I use "none"—it's not special, just a string I know won't match any test function names. This filters out test functions, even if there are none. It doesn't hurt, and I like to do it as a practice.

Then, I use the `-bench` flag with a dot to match all benchmarks—remember, it's a regular expression. So: run no tests, but all benchmarks. I'll also increase the benchmark time to three seconds using `-benchtime=3s`. This uses the basic time formatting from the time package—you can specify hours, minutes, etc. So we're extending from the default one second to three seconds.

Right now, I'm only looking at CPU performance, but let me show you another option: I can also measure memory allocations by adding `-benchmem`. When I include `-benchmem`, the tool will report allocation statistics. Remember, an allocation occurs whenever a value escapes the stack and is moved to the heap.

So the full command is: `go test -run=none -bench=. -benchtime=3s -benchmem`. Let's run it.

Okay, the test is running. For those on Darwin, we're on amd64 architecture. Looking at the results: Sprint ran 50 million times over the three seconds. Notice the total test time was almost nine seconds—about three to four seconds per benchmark. Sprint executed at 68 nanoseconds per operation, with 5 bytes allocated across one object—meaning one heap allocation of 5 bytes.

However, Sprintf ran 100 million times—faster, at 53 nanoseconds per operation—with the same memory allocation: 5 bytes over one object. In other words, our guess was wrong. Sprintf is actually faster than Sprint by nearly 15 nanoseconds per operation in this run. The difference is small, but the point stands: we cannot guess about performance. Do not guess. Do not guess.

When writing code, optimize for correctness, readability, simplicity, and integrity—those are the things you can know at development time. If you write code primarily for performance, especially new code, you're guessing, and you'll likely be wrong. Get something working first, then use benchmarks to validate.

So now we know that we might have chosen Sprint in our code, but from a performance standpoint, Sprintf would have been better—even if only by 15 nanoseconds per operation. That's precisely why we must not guess about performance.

We've just seen how to create a basic benchmark and run it from the command line.