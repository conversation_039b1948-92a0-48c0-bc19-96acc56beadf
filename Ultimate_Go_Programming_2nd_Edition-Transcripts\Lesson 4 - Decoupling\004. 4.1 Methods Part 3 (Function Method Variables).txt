Alright, so now that we've learned the guidelines around when to use value and pointer semantics, let's go back to that other code I showed earlier, where we were looking at a slice of user values and iterating over it. I always want to create a slice of values as my core data—that is the point of truth: those two values inside the slice literal. But we previously discussed whether, for a value of type user, we should be using value semantics or pointer semantics. Is it reasonable to make copies of people? And the answer, for me, is no. We really should be using pointer semantics for the user type.

I want to show you how mixing semantics can cause a huge amount of pain. Look at line 50. Notice that I'm using value semantics for my user collection. That's already a code smell—we shouldn't be making copies of users. Think about it: we're making a copy of the slice, which is fine, but now we're operating on a copy of the data. On the first iteration, we're not working with the original data—we're working with a copy. That's what value semantics are supposed to give us, so that in itself isn't the problem. The problem is that we shouldn't be working with a copy at all. We should be working with <PERSON> directly or <PERSON><PERSON> directly. But we're not. Here's the mix: I'm now using a pointer semantic API against a copy of the data. If I run this, <PERSON><PERSON><PERSON> or <PERSON><PERSON>'s email address won't change—the copy's email will change. This code is very hard to read and reason about. This is why I cannot stress enough: we must have semantic consistency throughout the codebase to avoid this kind of trouble.

One thing I want to make sure you understand is that methods are really just syntactic sugar—they don't fundamentally exist. All we have is state and functions. Methods give us the illusion that a piece of data has behavior. That's their purpose: to provide syntactic sugar, this belief system that data has behavior. And there are times when it's important for data to have behavior. But again, we need to learn when data should have behavior and when it shouldn't. I want data having behavior to be the exception, not the rule. This is something you have to consciously fight against because you've been trained to always allow data to have behavior. No, no, no—functions, for me, are almost always the better choice when it's reasonable and practical to use them.

Let's look at a few things in this code as we continue. Consider the type `data`. There it is—`data`—and it has two behaviors: `displayName`, which uses value semantics, and `setAge`, which uses pointer semantics. I also want to mention something quickly: setters and getters are not an API. If I see a function like this—or in this case, a method—called `set` in a code review, we need to have a conversation. Setters and getters bloat the codebase, require more testing, and add no real value. An API should provide meaningful functionality. We'll talk more about that during design. But simply setting and getting values does not provide value, and I really want to avoid these types of methods.

Here we are: `setAge` uses pointer semantics, and `displayName` uses value semantics. One thing I want you to see quickly is this: if I define a new type—let's say I call it `Bill`—does this new type `Bill` have the same behavior as `data`? Remember, `Bill` is a new named type based on `data`. But you need to understand that behavior is not inherited. There is no behavior associated with `Bill`. All the behavior remains associated only with `data`. Why? Because these methods are not declared inside the type like in a class-based system—they're declared outside. This is Go: separating state from behavior. We're not combining them; we're keeping them separate. The syntax itself enforces this separation. So even though `Bill` is based on `data`, that basing is based on the memory model, not on behavior.

Now, back to the `data` type. I have `displayName` and `setAge`. There's nothing new about how we call these methods. I create a value of type `data` on line 29—nothing unusual. Here's the data value with "Bill" inside it. Then on line 36, I call `displayName` using `d`, and `setAge` using `d`. Go adjusts to make the call, as usual. But what I really want to share is that, even though you'll see method calls in stack traces, the reality is that methods are syntactic sugar. They give us the belief that `d` has the behavior `displayName`, that `d` has the behavior `setAge`. But when you call `d.displayName` or `d.setAge`, you're really calling a function. The receiver is actually a parameter—it's the first parameter to the function.

When you call `d.displayName`, the compiler actually makes a call to `data.displayName`—that's the full function name—passing the receiver `d` as the first parameter. Similarly, when you call `setAge`, the real function name includes the pointer receiver: `(*data).setAge`. That's the actual name of the function. What you're seeing is Go adjusting to make the call. This adjustment is possible because the receiver is just data being passed in.

You are never, ever allowed to call a method like this directly—don't do it. If you do and say Bill showed you this, I'll throw you under the bus. I'll deny this was ever in the video. You must use the syntactic sugar when calling methods. But if you look at a stack trace during a panic, method calls will appear in this raw form. My point is that methods are syntactic sugar—they give us the idea that data has behavior. They do provide that abstraction, but technically, underneath, they're just functions. The receiver is truly a parameter, and it's the first one.

Now, I want to drive home more strongly the connection between value/pointer semantics and behavior. If you know which semantics are in play, you know the behavior. This is everything—it's where I've been trying to lead you since the start of this video.

Look at this code. On line 52, what are we doing? I'm getting the method `displayName`, already bound to `d`, but I'm not making the method call—notice there are no parentheses. I'm assigning it to a variable named `f1`. Functions in Go are values—they are typed values. That means we can pass a function by name anywhere in our program, as long as the type and signature match. Here, I'm creating a variable—a level of indirection—to access and call `displayName`. I'll be able to make the method call on line 55 through this variable.

But for that call on line 55 to work, certain things must be set up. `f1` is a reference type—it's a pointer. But if it's a pointer, it must point to something. It does: it points to a two-word data structure. The first word points to the code for `displayName`. Remember, `displayName` is really a function. So we have a level of indirection. I want to emphasize this: I now have two levels of indirection to reach the code. But I can't call `displayName` without `d`. I need `d`. But which `d` am I using? This is where semantics are critical.

Go back and look at `displayName`. It uses value semantics. Value semantics mean every piece of code operates on its own copy of the data. Copies are made. What does that mean for `f1`? It means `f1` cannot reference the original `d`—it must be given its own copy. This variable always operates on its own copy because `displayName` enforces value semantics. Also, we have indirection here, and in Go, indirection triggers escape analysis. But in this case, we've already made a copy. That means allocation—it has to allocate on the heap. We don't know this at compile time; it happens at runtime.

What you're seeing here is our first real introduction to decoupling in Go. `f1` is decoupling not only the code we want to execute but also the data we need to execute it against. Because we're making a copy of `d`, there's an allocation.

Great. Now, when we call `f1` on line 55, we have an indirect path to the code and we know what data to use. But on line 58, if I change the data—say, "You're no longer Bill, you're Joan"—will we see that change? Absolutely not. We're not operating on the original `d`. We're operating on our own copy. Why? Because value semantics are in play. And why are value semantics in play? Because `displayName` uses a value receiver.

Now, let's do this again. Look at the code where we assign `setAge` to a variable named `f2`. No problem. `f2` is a reference type—a pointer. It also has its own internal data structure. The first word points to the code for `setAge`. Again, we have double indirection, giving us decoupling. But what semantics are in play for `setAge`? `setAge` uses a pointer receiver—pointer semantics. That means it shares the data; it doesn't make a copy.

What does that mean for `f2`? This pointer doesn't point to a copy—it points to the original `d`. So when we use `f2` to call the method on line 73, we're operating on the original, shared data. That means on line 76, if we change "Joan" to "Sammy", we will see the change. Pointer semantics are in play.

This is interesting: we again have double indirection to reach the data. And the escape analysis algorithm has what we might call a limitation—once we have this kind of double indirection, escape analysis can't determine whether the value can stay on the stack. In other words, even though there's no logical reason for `d` to escape to the heap, it must be allocated there. So there's a cost to decoupling in Go.

It's important—we'll keep saying this: when you decouple concrete data in Go, you pay the cost of indirection and allocation. There's no way around it right now. We must ensure that when we decouple, the cost is worth it. We don't want to blindly decouple code in Go. We do it only when it's the right engineering choice—when it's reasonable and practical. Because this cost is real. Remember, allocation is number two on our list of performance bottlenecks. We can't take allocations lightly. But if the decoupling adds real value—if it minimizes cascading changes, if it lets us work with different concrete data without blowing up the codebase—then I'll accept the cost of allocation any day. Then it's worth it.

Alright, so methods give us the ability to attach behavior to data. But we still have to learn: when is it reasonable and practical for data to have behavior? I want this to be the exception, not the rule.