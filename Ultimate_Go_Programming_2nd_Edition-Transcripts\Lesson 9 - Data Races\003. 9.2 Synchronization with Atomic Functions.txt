Okay, so we've finished talking about cache coherency and false sharing issues, which are really interesting when it comes to multithreaded software. But now I want to start with a very simple data race problem. If we can look at a simple data race, you'll get a better understanding of what we mean by synchronization.

In this piece of code, we have a global variable on line 16 called counter. This is my global counter. What I want to do is launch two goroutines—goroutine one and goroutine two—that will increment this shared counter. Right now, we have a synchronization issue. It's like saying, "You get to go, you get to go, you get to go"—this isn't orchestration. The goroutines aren't talking to each other. They need to get in line and take turns when incrementing the shared state, which is the counter.

On line 21, I define a constant of two for the two goroutines and use a WaitGroup as the orchestration mechanism. Since we have two goroutines, we call Add(2) so we can manage when they're done and then shut down the program properly. Inside the loop, I use a literal function and place the keyword `go` in front of it, creating goroutine one and goroutine two.

Each of these goroutines performs three operations: a read, modify, write sequence. The goroutine reads the current value of the counter, modifies it locally, and writes it back. Another goroutine does the same: read, modify, write. Since each goroutine increments the counter twice, we should end up with a final value of four.

Let's navigate to where this program is located, build it with `go build`, and run it. Every time I run the program, I see the expected output: each goroutine performs its read, modify, write operations. Two goroutines doing two increments each should always result in four. And indeed, every time I run it, I get four. Great—I'm happy. This program seems to be working. We could put it into production.

But so far, we've been running on bare metal. One day, we decide to move to the cloud, where machines are much noisier and busier. Once we make that move, things start to change. Why? Because these operations are not atomic. To demonstrate this, I'm going to add just one more line of code: a print statement, which is a system call. I'll print the local variable's value before writing it back to the global state. Watch what happens when I add this line on line 38.

Let's build and run it again. Notice that the final counter value is no longer four—it's two. Also, the printed sequence isn't one, two, three, four. It's one, two, one, two. How did adding just one line of code suddenly change the behavior—and for the worse?

What I'm simulating here is the effect of moving to a noisier environment like the cloud. The print statement is a system call, and system calls can trigger context switches. Let's walk through what happens now.

The first goroutine reads the global counter, which is zero, and stores it locally. Then it reaches the print statement. The scheduler sees a system call and moves this goroutine from the running state to a waiting state—this is a context switch. The goroutine is taken off the processor (P), and now the second goroutine gets to run.

The second goroutine reads the global counter, which is still zero, increments its local copy, and then hits its own print statement. Again, a system call triggers another context switch. Now the first goroutine resumes. It has no idea that it was paused or that the global state may have changed. It writes its local value—1—back to the global counter.

Then it reads again, modifies, and another context switch occurs. The second goroutine resumes, but it still thinks the original value was zero. It writes back 1 as well. Both goroutines are operating on stale data. Neither is aware that the other has modified the shared state.

At the hardware level, when one core modifies a cache line, it marks it as dirty, but there's no automatic mechanism—like a snooping protocol—that notifies the goroutines their local copies are outdated. These goroutines are blind to context switches and cache invalidations. They don't know they were paused or that the global state changed while they were away.

This is why we're seeing incorrect behavior: only one goroutine effectively performs the increment, and the final result is two instead of four. The print statements can appear in any order—like one, two, one, two—because we're now dealing with true parallel execution across multiple cores. Each goroutine runs on its own M (machine), tied to its own core, and we have additional synchronization issues with output ordering. Don't focus on the print order; what matters is that the print statement introduced context switches in the middle of the read-modify-write sequence.

Before, we were getting lucky. There were no guarantees. Remember: synchronization and orchestration require that we *demand* them. You can't hope for synchronization—it must be explicitly coded. We lost it here because no line of Go code is atomic.

Look at the `++` operator. It appears to be a single operation, but it's actually a read, modify, write sequence. I've expanded it here for clarity, but even `value++` compiles to multiple assembly instructions. At the operating system level, any of those instructions can be interrupted by a context switch. Even at the machine level, those instructions can be broken down into micro-operations, any of which could be preempted.

There is no single line of code—even the one I've highlighted—that is atomic by default. You must demand synchronization through explicit coding. That is your responsibility.

So given this logic, how do we ensure that these three operations—or at least the increment—happen synchronously, especially in a multithreaded environment across multiple cores?

You have two choices: atomic instructions or mutexes. Atomic instructions are the fastest because they operate at the hardware level, where the processor handles synchronization. However, they're limited to small data sizes—typically four or eight bytes—so they're ideal for counters like this one.

When you have multiple lines of code that must execute atomically as a group, that's where mutexes come in.

Let's start by fixing this with atomic instructions. To do that, I'll use the `sync/atomic` package. This package provides a clean API. But when using atomic operations, you must use precision-sized integers—either `int32` or `int64`. You can't just use `int`, because the atomic package requires consistent size across platforms.

I've updated the counter to be an `int64`. I still have the constant two and the WaitGroup for orchestration. But now I've removed the three lines of read, modify, write code and replaced them with a single call to `atomic.AddInt64`.

If you've used atomic operations in other languages, this API will look familiar: `AddInt32` or `AddInt64` for atomic addition, `CompareAndSwap` which compares two values and swaps if they're equal, `Load` for atomic reads, `Store` for atomic writes, and `Swap` for unconditional atomic swapping. This is the basic set of atomic operations.

Synchronization works by passing the memory address as the first parameter. All atomic operations are synchronized at the address level. If two goroutines call `AddInt64` on the same memory address, they are forced to synchronize—one must wait for the other. But if they operate on different addresses, they can run in parallel without interference.

This is the fastest way to solve the problem. However, what if we wanted to keep our original three-line logic? I wouldn't recommend it for a simple counter, since `AddInt64` is the correct and efficient solution. But suppose we had a more complex sequence of three or more operations that must run together without interruption. In that case, we need a mutex.