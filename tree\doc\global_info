# An example of a possible global info file.
/bin
	Binaries needed before a /usr is mounted (single user mode).
/boot
	Kernels / boot loader files
/dev
	Device files
/etc
	System-wide configuration files.
/etc/rc.d/
	Slackware init scripts
/etc/X11
	X windows system configuration files.
/home
	Typically user home directories
*/lost+found
	Contains files recovered by fsck in the event of file-system damage
/lib
/lib64
	System libraries (essential for binaries in /bin, /sbin)
/media
/mnt
	Spare, usually temporary mount points, usually for removable media
	(/media)
/opt
	Optional local packages
/proc
	System & process information virtual pseudo-filesystem (man 5 proc)
/root
	Root's home directory
/run
	System runtime files
/sbin
	System (admin) binaries needed before /usr is mounted
/srv
	Files served by the system (e.g. web service)
/sys
	System information pseudo-filesystem
/tmp
	System wide temporary files, not guaranteed to be preserved between
	reboots
/usr
	Read-only data, historically a secondary hierarchy to be mounted after
	'/', so binaries / data that might not be available during boot.
/usr/bin
	User binaries, not needed at boot.
/usr/include
	Standard include libraries (C, C++)
/usr/lib
/usr/lib64
	Libraries (for binaries in /usr/bin, /usr/sbin)
/usr/local
	A tertiary hierarchy for local system additions
/usr/sbin
	Non-essential system binaries (e.g. daemons)
/usr/share
	Shared data
/usr/src
	Source code (e.g. kernel source)
/usr/X11R6
	X windows (Version 11, release 6)
/var
	Variable files, files that will likely change during run-time.
/var/cache
	Application cache data.
/var/lib
	State information generated by programs such as databases and
	package managers
/var/lib/pkgtools/packages
	Slackware package database
/var/lib/pkgtools/setup
	Slackware setup/configuration scripts
/var/lock
	Lock files
/var/log
	log files (syslogd, klogd, httpd, other daemons)
/var/mail
	User mailboxes
/var/spool
	Spool directories for tasks waiting to be processed (cron,
	mail, print files) (depreciates /var/spool/mail)
/var/tmp
	Temporary files to be preserved across reboots
/proc/1
	Each one of these subdirectories contains files and subdirectories exposing
	information about the process with the corresponding process ID. Underneath
	each of the /proc/[pid] directories, a task subdirectory contains subdirectories
	of the form task/[tid], which contain corresponding information about each of the
	threads in the process, where tid is the kernel thread ID of the thread. The
	/proc/[pid] subdirectories are visible when iterating through /proc with getdents(2)
	(and thus are visible when one uses ls(1) to view the contents of /proc).
/proc/self
	When a process accesses this magic symbolic link, it resolves to the process's own /proc/[pid] directory.
/proc/thread-self
	When a thread accesses this magic symbolic link, it resolves to the process's own /proc/self/task/[tid] directory.
# Could go on to add all of man 5 proc for example here.