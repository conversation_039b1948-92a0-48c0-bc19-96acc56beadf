Now let's look at another pattern that can leverage a buffered channel: the drop pattern. Drop patterns are very powerful tools for reducing back pressure when things go wrong. They help us identify when the system is under stress and allow us to proactively reduce load. A good example of where a drop pattern is useful is in a DNS server. What are the chances that such a server might get attacked? Well, it's not a matter of if—it's going to happen constantly. The goal of a DNS attack is to flood the server with so many requests that it becomes overwhelmed trying to process them all.

The drop pattern helps us define our system's capacity—specifically, the maximum number of pending requests or tasks we can handle before we must start rejecting new work. It's easy to say yes, but you have to learn how to say no. At some point, you need to know your limits and enforce them. With a drop pattern, we say, "I'm sorry, I can't handle your request right now—I'm at capacity, and I won't compromise the stability of the system for this." Determining this capacity typically requires integration tests and load testing. It may also depend on available memory, CPU capacity, or other system-specific factors, and could even be dynamic depending on the environment.

Drop patterns are extremely valuable because they help us detect failures quickly, stop the bleeding, and allow the system to recover once the issue has been resolved. Let's examine a basic implementation of the drop pattern.

We start with a constant value of five, representing our capacity. We create a buffered channel with a capacity of five—this buffer represents the maximum number of pending tasks we're willing to accept. Once there are five tasks waiting in the buffer, we consider the system at full capacity. We will not accept a sixth task. Instead, we drop the incoming work immediately. Accepting more would introduce unacceptable risk.

Looking at line 194, we see that in the main function, we've created this buffered channel of size five—our defined capacity. We then launch a goroutine. While this could easily be a pool of goroutines, we keep it simple here for clarity. This single goroutine runs a for-range loop, receiving from the buffered channel on line 195.

The idea is that this goroutine continuously waits to receive data from the channel. As soon as data arrives in the buffer, the send occurs before the receive, but once the data is present, the receive operation pulls it out. More data comes in, it gets pulled out—this goroutine processes one task at a time. After processing a task, it returns to wait for the next one. If no data is available, it blocks on the channel receive, which is the normal behavior of a for-range loop over a channel. Eventually, when the channel is closed, the loop terminates.

Now imagine we're reading from a network source—let's say right around line 201. After setting up this pattern, we begin reading data from the network and immediately attempt to send it into our buffer. But once the buffer reaches five items, we know two things: first, we can no longer accept new data; second, any additional data must be dropped. We do not block. This also implies that if the buffer fills up, it might mean our processing goroutine isn't fast enough or has encountered an issue and isn't returning to receive new tasks promptly.

The health of our system is reflected in the state of this buffer. If the processing goroutine keeps up, the buffer never fills. But if something goes wrong and the buffer becomes full, we don't introduce blocking latency—we drop the excess work. To make this work effectively, we need a way to attempt a send without blocking, so we can detect when the buffer is full and act accordingly.

This is where the select statement comes in. Inside a loop that simulates 20 network reads, we use a select to attempt a non-blocking send. The power of select is that it allows a single goroutine to manage multiple channel operations—sends or receives—concurrently, enabling event-driven logic. This same mechanism supports cancellation, deadlines, and patterns like dropping, which behave similarly to cancellation.

Here’s how it works: we have a select with a case for the channel send. We've read data from the network and are trying to send it on line 203. If the send can proceed immediately—because there's space in the buffer—the data is added, everything proceeds normally, and we return to the top of the loop to read the next piece of data. As long as sends don't block due to available buffer space, the system runs smoothly.

But if the buffer is full—meaning all five slots are occupied—the send would block. We don't want to pay that latency cost. That's where the default case in the select comes in. The default case says: if the send would block, don't block—just proceed immediately. We move on. At this point, we decide what to do with the data we just read from the network: we drop it. We might send back a 500 error, or some indication that the request couldn't be processed. The key point is that we avoid adding latency and we don't create back pressure upstream.

Either we can handle the request, or we can't. When we can, great—we process it. When we can't, it's a strong signal that there's a problem in the system. Drop patterns are therefore essential—they provide a clean, immediate way to detect when we've reached capacity and are potentially failing. They reduce back pressure not through timeouts, but through capacity-based rejection.

We use the buffered channel as a tool to detect failure states. And when the buffer begins to drain again—when processing catches up—we know the system is recovering. Drop patterns are among the most powerful and important concurrency patterns you'll use, alongside worker pools and fan-out patterns, for orchestrating robust, resilient systems.