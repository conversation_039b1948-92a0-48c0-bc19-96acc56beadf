I can smell interface pollution almost from a mile away, and normally it's the result of starting with interfaces instead of concrete implementations. Up until now, I've been showing you how powerful it is to start with a concrete implementation and refactor into decoupling, focusing on data all the way through. But I want to show you a piece of code that might help you understand a bit more about where interface pollution comes from.

A friend of mine implemented some boilerplate code for a TCP server and asked me to do a small code review. As soon as I opened the source file, I saw this interface at the top: Server interface with methods start, stop, and wait. Immediately, that smelled very bad to me. Remember, an interface should describe behavior—and "server" isn't behavior. A server is a thing, a concrete entity, something real. That alone is a red flag.

Another smell is that the interface has three methods: start, stop, and wait. When I think about what a TCP server actually is, these three methods appear to model an entire API surface. It's unlikely that every part of the code needs all three behaviors all the time. But my first question was: "Are you asking the user to implement this behavior?" He said no—he had implemented it himself. I replied, "I thought so." Then I asked, "Do you have multiple implementations of a TCP server in your codebase?" He said no, just one implementation. That's another red flag. There's no need for decoupling if there's only one implementation and the user isn't providing any implementation details.

So I asked him, "Why is this interface here? It doesn't seem reasonable or practical." And he gave me the one answer you never want to hear: "The interface is there because we have to use interfaces." No. You do not have to use interfaces. You have to make engineering choices. Remember, interfaces come with the cost of indirection and allocation. This interface serves absolutely no purpose. Without even looking at the rest of the code, I already knew there was pollution.

When I looked at the rest of the code, it became even clearer. There's a concrete implementation defined as an unexported type, and the factory function returns a value of that type—but as the interface. That's wrong. Factory functions that initialize concrete types should return the concrete type directly. The caller can handle any necessary decoupling if needed. All of this is a very strong code smell.

The interface defines a method set that mirrors the entire API. Think about how a user interacts with this: they call new server and then use the API. If I replace the interface with the concrete type in the return signature, the calling code doesn't change at all. In fact, it gets better—potentially reducing allocations. This is major, major pollution.

It took me about half an hour to talk my friend into removing the interface. But there are some key smells I want to reiterate. First, I saw an interface that matches the entire API of the concrete type. That's wrong. Interfaces should define behavior—only the behaviors needed to decouple specific parts of the API that actually require decoupling.

Second, the interface is exported, but the concrete type is unexported. Yet we're returning that unexported type to the user through the interface. That's a big smell. The factory function should return a concrete value or pointer, not an interface.

And if I remove the interface, nothing changes from the API's perspective—because the interface wasn't actually decoupling anything. What I wanted to do with this code, and what I advised my friend to do, was get rid of the interface entirely. It's pure pollution. Use an exported concrete type, implement the factory function to return a pointer to that exported type, build out the API, and let users work directly with the concrete type. There's only one implementation, and the user doesn't need to provide any implementation details.

Here are some guidelines we should follow going forward. Use an interface when the user of the API needs to provide implementation details that you can't implement yourself. Or when you have multiple implementations and need a layer of decoupling around that concrete data. Or when you've identified parts of your API that require decoupling due to potential change.

Always question an interface whose sole purpose is to make the API testable. I cannot stress this enough: if someone says, "The interface is there because I need to mock for testing," I'm going to scream. Mocking isn't always the right answer. If you're mocking a database, you're wasting time. Those tests are useless because mocking a database doesn't accurately validate whether your code works. With tools like Docker today, you can spin up a real database in a container when you run go test and verify that your database interactions actually work—no mocking required.

There are many things people mock unnecessarily. In many cases, we can use real data for testing, or leverage Docker to stand up the actual systems. I'm not saying you should never mock—there are smart engineering decisions to be made. For example, a third-party API might be a valid candidate for mocking if you know its expected behavior and don't need to hit the real service. But for databases and certain other systems, mocking is a waste of time.

So don't justify an interface by saying you need it for testing. Our APIs are for users. They must be well-designed and testable, but we shouldn't compromise usability just to make testing easier. If an interface isn't providing real decoupling, and it's not clear how it's improving the code, then it probably isn't. In that case, consider removing it and sticking with concrete types.