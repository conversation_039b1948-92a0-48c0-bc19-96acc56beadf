So, there are two things that we do with slices all the time in Go code: we append values to them and we take slices of them, which is where the name comes from. Let's start with the idea of appending. Let's go ahead and start right here on line 13. This is very interesting—I'm using the keyword `var`. Remember, `var` is an indicator that we're using the zero value. In fact, what we're doing here is creating a slice named `data` set to its zero value. So, this slice looks like this: a zero-value slice would still be three words, but everything is set to its zero value—all those bytes are set to zero, their zero bit. And we call a slice that is set to its zero value a *nil slice*. Remember, when it comes to a reference type, any time a reference type is set to its zero value, we consider it to be nil—interfaces, channels, maps, slices, functions, there you go.

But I want you to see something here, which I find very interesting. I find lots of things interesting with <PERSON>, but this is one of them. We talked before about how, when dealing with struct types, an empty struct literal gives you the zero value. Here's a case where the empty literal for a slice does *not* give you a zero value. What the code does on line 14 is create what is called an *empty slice*. That means the empty slice isn't zeroed out, and there isn't a nil pointer—but although the length and capacity are zero, we still do have a pointer. Now we need this semantic because imagine you made a database call and the call completed—it wasn't an error, but it didn't bring back any data. That's not really an error; it just means the query had an empty result. So, this concept of "empty" is a really important semantic when it comes to collections, and this is what Go is giving us.

So, we use `var` for zero value—we get a nil slice, which we might want to return on an error. If we use the empty literal construction like you see on line 14, we end up with an empty slice, and that would mean everything was okay, there was no error—it's just that there wasn't anything. Now, I'm sure you're asking, "Bill, if this is empty and there's a pointer, where is it pointing to?" There's another special type in Go called the *empty struct*. The empty struct is declared like this: `struct{}`. What's interesting about the empty struct is that it's a zero-allocation type. I can create a million values of this empty struct, and there would be zero allocation. That's because there's an eight-byte value tied inside the runtime—like a global variable—that this empty struct is referencing. It means all million empty struct values would have the same address. So, basically, this pointer points to the empty struct. That's how Go represents "empty"—really cool stuff.

Okay, let's go back to our original code here. This code is showing you appending. We're going to start with our nil slice. It makes sense to start with a nil slice anyway, because unless you know exactly how much data you need, we don't want to pre-allocate the backing array—that is a waste. Remember, Go is all about minimizing resources. We'll take a small performance hit to do that, but even that performance hit won't show up on a profile. It's so low in the areas where we're going to be losing performance. So, let's start with the nil slice when we don't know. If we *do* know exactly what we need, then it *is* inefficient to do that—we should use the `make` call to pre-allocate length or capacity.

So, we're starting with our nil slice, and what this code does is go ahead and add, say, 100,000 strings—five zeroes, so 1e5, which is 100,000. My brain is shutting down—I can do that math quickly. So, we're going to add 100,000 strings to our nil slice, just keep appending values up to 100,000 of them. And I want to show you how this works. The program outputs every time changes are made to the backing array for the slice.

Now, what you also see here on line 22 is our `Sprintf` function. We're just generating a string based on the record ID, and then we go ahead and call `append`. Notice that `append`—notice the API for `append`—this is important to me: the API for `append` is really using value semantics. I love `append` because `append` is what we call a *value semantic mutation API*. Notice that `append` does mutate, but we're not using pointers, we're not sharing. `Append` gets its own copy of the slice value, mutates it, and returns it back. This is critical. `Append` is able to do mutations in isolation without causing side effects because it's leveraging a value semantic mutation API. Plus, I told you: slices should be using value semantics. We'll talk more about that when we get to methods, but it's using value semantics, so the APIs have to respect it.

Okay, great. Let's walk through the first five appends here, and we'll get a sense of how `append` works, and then we'll run it for 100,000. On the very first call to `append`, the `append` function gets its own copy of the slice value. Again, we're using value semantic mutation—there it is. Now, `append` is really just going to ask one question: it wants to know if the length and the capacity of the slice value we just got a copy of are the same. Because if they are the same, that tells `append` that it doesn't have enough capacity for this append, and it needs a new backing array. In this case, there isn't any backing array at all. So, it will create one. It will mutate, in isolation, its own copy of the slice value, make that modification, and now return a copy of that slice value back to the caller. Everything is nice and safe and clean thanks to our value semantics, and the caller now gets a new slice value pointing to a new backing array.

Let's do it again. We call `append` again. It gets this copy again. It asks: is length and capacity the same? The answer is yes. Oh, we've got no room for this append. Let's allocate a new backing array. We now get a new one. This means we have to copy A into here, and now we go with B on the append, which also means we're going to mutate the slice to point here, and now we have a length and capacity of two. Brilliant. We return that back by copying it, and a couple of things now happen: our slice value is now pointing to this backing array. In fact, at this point, we have no slice values pointing to the original backing array. What does that mean? It means on the next garbage collection, this can get swept away.

There are lots of little interesting things happening if we just stop here for a second. The first thing is that "sweep" makes me think of this: what is a memory leak in Go? What is a memory leak in a programming language like C or maybe C# or C++ where you get a function called `new` or `malloc`, and you get to allocate memory? In programming languages where you're responsible for those allocations to the heap, you have a responsibility to free it. That type of memory leak just means you've allocated something without the corresponding call to `free` or `delete`. We can instrument code for that. That's not really a problem when we do that in those programming languages.

But in Go, escape analysis is determining the allocations, right? When `append` had to create that backing array, that's happening on the fly—it's on the heap. This is in the heap. We don't know about these things at compile time. These are all heap allocations. This garbage collection time is costing us. But what *is* a memory leak in Go? A memory leak in Go is when you maintain a reference to a value in the heap, and that reference never goes away. This is complicated because you can't instrument for memory leaks when it's reference-based. Who is to say that a reference is or isn't supposed to be there at any given time?

So, if you think you have a memory leak—which is the only way to look at the GC trace—I'll show you how to do that at the end of this class video when we start getting into tooling. But if we look at a GC trace, what we're going to want to see is: is the memory going up on every garbage collection? And if it is, we have a memory leak. And right away, one of the first things I'm going to ask you to do is take a look at this: the first thing we're going to look for is—are you creating extra goroutines with paths of execution? Are they supposed to be terminating and they're not? It's a classic memory leak: both the goroutine is leaking, and anything that goroutine was holding onto never gets released because it never terminates. That's a very classic place in Go where you can have memory leaks. We're going to look for goroutines first.

The next thing we're going to look for is possible maps. Lots of people like to use maps for caches. And it's great, but at some point you have to delete keys—entries in your map. You have to either do it based on time, or based on an event, like an operation or something dropping. You have to do it. You have to at some point delete keys from a map when things happen, or the map just continues to grow and grow and grow. So, maybe we can argue it's not a memory leak, but resources aren't forever. So, you either have to do it on time, or in some sort of event—you have to make a choice on when the map keys get deleted. You can't grow it forever.

Another place where you could have a leak is right here on an `append` call. One of the very first things I'm going to look at on an `append` call is: is the slice value being copied the same exact one that we're going to replace on return? If I saw something like this, this would be very, very scary to me, because this could potentially mean we're holding a reference to old backing arrays because we're not replacing certain things going in. So, I'm going to be looking for this because I want to make sure that we replace this and therefore reduce or remove those references to the old backing data structure. These are all really, really important.

The last step might be APIs that we know about where you're supposed to call `close`. And if you forgot to call `close`, that's a classic, classic memory leak.

Okay, let's keep going here. We've done two. Now, just for efficiency, we already know we're making a copy, so I don't have to draw the copy—we'll just talk about what comes back out. `Append` prepares a copy of this to append. `Append` looks at length and capacity, says they're the same, goes: okay, I don't have any more capacity, so I'm going to go ahead, double the backing array, copy A and B, and append C. And now the new mutation looks like this: we now point here. Our length is three and our capacity is four. This is the first time you're seeing a situation where the capacity is greater than the length, and it's happening naturally because we've doubled the size of the backing array and we only added one to its length—because that's all we ever do on `append`: we add one to the length. We have a length of three, capacity of four. This is great. We have an element of capacity now for efficiency and growth, which is going to happen on the next append.

On the next append, we ask: is length and capacity the same? The answer is no, it's not the same. This is brilliant. We don't have to do a copy anymore. We can just bring that capacity into the length. Boom—four and four. But what happens on this very last one? We're going to do one more on the board. Length and capacity are the same—okay, no problem. Now we're going to double the size of the backing array again. We're going to have to copy all this down. And what are we going to end up with? Well, length increases by one—from four to five. And in this case, the backing array is doubling in size—from four to eight—and now we have three extra elements of capacity ready to go without the cost of the copy.

Okay, I want to run this for 100,000 iterations and show you something interesting about the `append` algorithm. But you can't change the `append` algorithm—it's there. What's interesting about the `append` algorithm is that when we finally get to a slice that has a backing array of a thousand elements, you see: before a thousand elements, `append` doubles the size of the backing array on growth. That's what I was doing on the board. But once we get to a thousand elements, the algorithm uses 25% growth over time. So, the backing array won't double, but it will grow by 25%, which gives us 25% efficient growth moving forward without the extra allocation.

But everything you're seeing here on the screen in the output of this program is technically an allocation. All of these allocations had to take place to add the 100,000 strings. Now, what's interesting here is we know what this is: it's 100,000 strings. We know what our capacity already is. So, ideally, if we were going to be more efficient, I wouldn't want to start with the nil slice. This is an opportunity where we can set the capacity ahead of time. Notice I'm setting length to zero and capacity to 100,000—why? Because `append` works off of length. If I set the length to 100,000, then we're going to have 100,000 zero-valued strings and another 100,000 actual strings. You've got to remember that `append` works off of length.

Now, if I run this, we won't see any output—why? Because we never have to make a copy of the backing array. That never has to happen. But we can be even more efficient here. We could literally just get rid of the `append` call by just indexing everything that we need. If we start at zero here, then we could just use the index position and do the assignment. This is even more efficient because we don't have the function call anymore. But now this is a case where we have to set up the length, because we've got to make sure that all of that is available for use up front.

So you can see here that if we don't know what our capacity is, then guess what? We don't know length and capacity—let's start at nil. `Append` will help grow and do that as efficiently as it can. But if we *do* know exactly what we need up front, let's set that length and capacity, get away from the `append` call, and then everything is going to be great.