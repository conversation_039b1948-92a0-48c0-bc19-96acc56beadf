Alright, let's go explore a little bit deeper the idea that we're passing concrete data across these program boundaries when we're using interface values. We could call this implicit interface conversion, but again, I really want to focus on the fact that it's the concrete data that we're moving. I want to look at more of the mechanics behind it just to solidify this a little bit more.

I've got the mover interface, the locker interface—those are move, lock, and unlock. Then I've got the moveLocker, which is the composition of multiple interfaces. So a moveLocker knows how to move, lock, and unlock. We've got the three interfaces right there. Then I've defined a concrete type named bike. This is declared using the empty struct, which is a case where you might want a method-based API for some reason when there's no state. Maybe we're using bike as a namespace. We need to be very careful here—remember, functions are always going to be more precise than methods—but in this particular case, since there's no state, the API would have to be as precise as any function. We've got the implementation of move, lock, and unlock. So a bike is a mover, a locker, and a moveLocker.

Okay, great. Now this is where things get fun. On line 49 and line 50, we're declaring two interface values set to their zero value: that's the moveLocker and that's the mover, both set to their zero value, a nil interface. Then on line 54, we construct a bike value—that's our bike, an empty struct—and we assign it to the moveLocker interface. We can do that because we've implemented the required methods, and if we go back and look at the interface implementation, it's using value semantics. Value semantics means we can store copies of values or addresses, and we're using value semantics here. So this means we have our bike, and we're pointing to that bike value. Remember, it's an empty struct, so one could argue we really have a copy of the bike. But since there's no construction happening elsewhere and no variable, let's just view it as "there's our bike."

Now, on line 58, we can assign ml to m—two interface values—but we're not really assigning the interface values themselves. Remember, the only thing that's real is the bike. What we're really saying is: let's store the concrete type bike inside of m. The compiler knows whether this concrete type has all the behaviors needed to satisfy m because of the interface declaration. We know that any concrete type inside moveLocker must be able to move, lock, and unlock—all three behaviors exist. Therefore, since the only behavior m requires is move, we can perform the assignment. Now we've got a bike in m, and boom, it's done.

However, we can't go in the other direction. I can't assign a mover to a moveLocker. Why? Because from the mover's perspective, the only behavior we're aware of is move. That's the only thing we know. We don't know if there's lock and unlock in the concrete data when viewed through the mover interface. From the moveLocker perspective, we do know. So that assignment isn't allowed.

Go has something else that's very special: type assertions. A type assertion allows you to ask a question about what concrete data is stored inside an interface. It lets you ask a question at runtime—note, not at compile time. Everything I've explained so far can be checked at compile time, but type assertions happen at runtime.

On line 76, we're asserting: I believe that at this moment in time, the concrete value inside m is a bike. That's the question we're asking. If it's true, and since we're using value semantics, b will be a copy of that stored bike. If it's false, we panic—there's an integrity issue. You're saying there absolutely must be a bike in there, and there isn't.

But there's a second form of type assertion that includes an "ok" value—a Boolean flag. We use this when we're not certain the concrete type matches. What this form says is: I believe there's a bike value inside m. If there is, ok is true and b is a copy of the bike. If ok is false, then there isn't one—no panic. We acknowledge the possibility that the assertion might fail. In that case, b is set to its zero value—the zero value of bike.

Type assertions are very powerful. Let me show you some examples of how they work well in the standard library. One of the best places to look is the Go documentation for the io package. I'll search for the copy function. Whenever you're in Go docs and want to see the source, just click the header link—it takes you right to the code.

I want to look at the unexported implementation of copyBuffer. Notice it takes any concrete data that implements Writer and any that implements Reader. Look at the very first if statement. It says: if the concrete data stored in the reader variable (the source) also implements this other behavior—notice we're type asserting not to a concrete type but to another interface—then let's bypass the default copy behavior and use this custom one. This allows users to provide their own optimized implementation. Type assertions are powerful in API design because they let you define a default implementation while allowing interfaces to override it when needed.

Let me show you another example. Here I have a concrete type named user. It implements a method called String using pointer semantics. Actually, more is going on: this String method, which returns a string, implements the Stringer interface in the fmt package. This is how we override the default output behavior of fmt. But we're using pointer semantics, which means only pointers satisfy the interface. If I pass a pointer to fmt, I get to override the default. If I pass a value, I don't—I get the default behavior.

Let's see this live. I create a value of type user. On line 29, I use value semantics; on line 30, pointer semantics. With value semantics, even though the interface is implemented with a pointer receiver, I get the default output—the standard struct formatting. But when I pass the address of u, I override the default thanks to the String method. Essentially, the call to Print performs a type assertion: it asks, "Does this concrete data also implement the String method?" If not, like on line 29, no problem—we use the default. If yes, like on line 30, we override.

This is a powerful API design pattern made possible by type assertions. It allows a standard implementation while not binding the user to it. It says: "If you have a better way—maybe because of your OS, architecture, or domain knowledge—I give you the opportunity to override the default." Type assertions happen at runtime and are a very powerful part of designing flexible, extensible APIs.