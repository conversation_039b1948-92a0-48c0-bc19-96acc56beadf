  Please read the INSTALL file for installation instructions, particularly if
you are installing on a non-Linux machine.

  This is a handy little utility to display a tree view of directories that
I wrote some time ago and just added color support to.  I've decided that
since no one else has done something similar I would go ahead and release
it, even though it's barely a 1st year CS student hack.  I've found it damn
handy to peruse a directory tree though, especially when someone is trying to
hide something from you.

  The main distribution site for tree is here:
  http://oldmanprogrammer.net/source.php?dir=projects/tree

  Backup GIT sites are:
  https://gitlab.com/OldManProgrammer/unix-tree
  https://github.com/Old-Man-Programmer/tree

  Current e-mail address to reach me at: <EMAIL>

  If you don't like the way it looks let me know how you think it should be
formatted. Feel free to suggest modifications and additions.

  Thanks go out so the following people who have helped bring tree to the
pinnacle of perfection that it is: ;)

Francesc <PERSON>r
  - Added HTML output (-H).
  - Added options -o, -L and -<PERSON>.

<PERSON>
  - Added -S option to print ASCII graphics lines for use under linux
    console when an alternate console font has been selected (might also
    work under DOS telnet).

<PERSON> (and others)
  - Made tree more portable.  Should compile under solaris.

<PERSON><PERSON><PERSON>
  - Discovered bug where tree will segmentation fault on long pathnames.
  - Discovered in -L argument processing.

<PERSON>age
  - Discovered problem with recursive symlink detection

A. Karthik
  - Suggested option to remove file and directory report at end of tree
    listing.

Roger Luethi
  - Spotted memory over-allocation bug in read_dir().
  - Submitted several patches to fix various memory leaks.

Daniel Lee
  - Reported that Tru64 defines TRUE/FALSE in sys/types.h (OSF1 standard?)

Paolo Violini
  - Found bug in tree that caused it to seg-fault if 50 file arguments where
    given and directory coloring was turned on.

Mitsuaki Masuhara
  - Discovered tree crashed on missing arguments.
  - Discovered that tree did not properly encode characters in filenames
    when used as URLs when using the -H option.
  - Fixed issue with --charset option processing.

Johan Fredrik
  - Pointed out that tree did not list large files.

Ted Tiberio
  - Submitted patch which fixed a compiler issue and cleaned up HTML and CSS
    code, applied CSS to all output, and fixed up HTML to 4.01 strict
    standards.

David MacMahon
  - Added '|' support to the pattern matching routines.

Dan Jacobson
  - Pointed out that -t did not sort properly for files with the same
    timestamp.
  - Suggested option to change HTML title and H1 string.
  - Suggested -r option for reversed alphanumeric sort ala 'ls -r'.

Kyosuke Tokoro
  - Provided patch to support OS/2, fix HTML encoding, provide charset
    support. Added to authors list.

Florian Ernst
  - Debian maintainer who pointed out problems and applied fire to feet to fix
    stuff.

Jack Cuyler
  - Suggested -h option for human readable output for -s, ala ls -lh.

Jonathon Cranford
  - Supplied patch to make tree under cygwin.

Richard Houser
  - Provided patch to fix a colorization bug when dealing with special
    files and directories that seem to have an extension.

Zurd (?)
  - Suggested removing trailing slash on user supplied directory names if -f
    option is used.

John Nintendud
  - Pointed out broken HTML output in 1.5.1.

Mark Braker
  - Suggested --filelimit option.

Michael Vogt
  - Suggested -v option (version sort).

Wang Quanhong
  - Provided build options for Solaris.

Craig McDaniel
  - Provided build options and source mods for HP NonStop support.

Christian Grigis
  - Noted that setlocale() should come before MB_CUR_MAX check.

Kamaraju Kusumanchi
  - Submitted patch to remove compiler warnings for Solaris.

Martin Nagy
  - Provided patch which fixes issue where indent may output more than it
    should when dirs[*] is not properly cleared before use.

William C. Lathan III
  - Showed that tree was not properly quoting arguments to recursively called
    tree instances when using -R.

Ulrich Eckhardt
  - Submitted patch for --si option.

Tim Waugh (redhat)
  - Pointed out a potential memory leak in listdir().

Markus Schnalke
  - Tracked down bug where tree would print "argetm" before the filename of a
    symbolic link when the "target" option was specified for LINK in dircolors.

Ujjwal Kumar
  - Suggested that tree backslash spaces like ls does for script use.  Made
    output more like ls.

Ivan Shmakov
  - Pointed out multiple CLI defenciencies (via Debian)

Mantas Mikulnas
  - Provided patch to make tree more reliably detect the UTF-8 locale.

Tim Mooney
  - Noticed S_ISDOOR/S_IFDOOR spelling mistake for under Solaris.

Han Hui
  - Pointed out possible memory overflow in read_dir (path/lbuf not equal in size
    to pathsize/lbufsize.)

Ryan Hollis
  - Pointed out problems with the Makefile w/ respect to OSX.

Philipp M?ller
  - Provided patch for filesize sorting.

Sascha Zorn
  - Pointed out that the HTML output was broken when -L 1 option was used.

Alexandre Wendling
  - Pointed out that modern systems may use 32 bit uid/gids which could lead
    to a potential buffer overflow in the uid/gid to name mapping functions.

Florian Sesser
  - Provided patch to add JSON support.

Brian Mattern & Jason A. Donenfeld
  - Provided patch to add --matchdirs functionality.

Jason A. Donenfeld
  - Added --caseinsentive, renamed --ignore-case option.
  - Bugged me a lot.

Stephan Gabert
  - Found a bug where the wrong inode (and device) information would be printed
    for symbolic links.

Nick Craig-Wood
  - Fixed issue where mbstowcs() fails to null terminate the string due to
    improper UTF-8 encoding leading to garbage being printed.

Mantas Mikulėnas
  - Fixed issue with malformed multibyte string handling.

Wagner Camarao
  - Pointed out that JSON size output ignored -h/--si flags

John Lane, Tad, others
  - Fixed JSON output hanging commas

Jacob Wahlgren
  - Improved command line switch error reporting.
  - Symbolic links not displayed if a -P pattern is active
  - Missing argument error reporting fixes on long format switches.

Shawn Mehan
  - Update BINDIR in Makefile for MacOS X -- It is not allowed to install
    programs to /usr/bin on MacOS X any longer due to System Integrity
    Protection (SIP)

Kirill Kolyshkin
  - Some man page fixes and cleanups

Alyssa Ross
  - Suggested adding support for BSD's CLICOLOR and CLICOLOR_FORCE environment
    variables.

Tomáš Beránek
  - Make sure we always use xmalloc / xrealloc

Sergei Maximov
  - Make XML/HTML/JSON output mutually exclusive.

Jonas Stein
  - Deprecate using local -DLINUX / -DCYGWIN and use the OS provided defines

John A. Fedoruk
  - Suggested --filesfirst option.

Michael Osipov
 - Optimized makefile, HP/UX support.

Richard Mitchell
  - Suggested --metafirst option

Paul Seyfert
  - Honor -n (no color) even if the CLICOLOR_FORCE environment variable is set

Filips Romāns via Debian
  - Make tree colorization use reset (rs code in dir_colors,) not normal color
    when resetting attributes.

Chentao Credungtao via Debian
  - Properly sort --fromfile input

Jake Zimmerman (and others)
  - Suggest support for .gitignore files (--gitignore option)

Eric Pruitt
  - Always HTML escape filenames in HTML output even when -C is used.

Michiel Beijen (and others)
  - Suggest Support multiple -I and -P instances.
  - Suggest that / match directories in patterns (also Taylor Faubion)
  - Suggested to update MANPATH for OS X

Michal Vasilek
  - Various Makefile fixes

Josey Smith
  - Reported an error with * in the patchmatch code where *foo*bar would match
    *foo alone.

Maxim Cournoyer
 - Reported HTML url output issue w/ 2.0.0-2.0.1

Ben Brown
 - Updates to the Makefile
 - Reported use after free error

Erik Skultety
 - Reported same use after error

Saniya Maheshwari / Mig-hub ? / Carlos Pinto
 - Reported various issues with --gitignore

Piotr Andruszkow
 - Suggested adding support for --info and --gitignore for the --fromfile
   option.

Sebastian Rose
 - Another attempt at fixing extraneous /'s in HTML URLs/output.

Dave Rice
 - Fixed XML output

Timm Fitschen
 - Suggest adding support for the NO_COLOR environment variable.

Chentao Credungtao
 - Suggested supporting symbolic links in --fromfile (--fflinks option)

Sith Wijesinghe and Matthew Sessions
 - Remove many C90 isms to make compiling with C90 compilers easier.

simonpmind (gitlab)
 - Reported issue where following links while doing JSON output would lead to
   incorrect JSON output.
 - Suggested suppressing 'recursive, not followed' when using -L, fixing JSON
   error.

German Lashevich
 - Reported an issue where .info patterns relative to the .info file that did
   not use a wildcard for matching the prefix were not matching files properly.

Javier Jaramago Fernández
 - Reported a buffer overflow in listdir() when file names are allowed to be
   longer than 256 characters (like when using fromfile.)

6ramr (gitlab)
 - Reported issue with following symbolic links when a full tree was gathered.

Clinton
 - Reported issue where --gitignore does not think a pattern with a singular
   terminal '/' (indicating it matches only directories,) is a relative path.

jack6th (gitlab)
 - Don't prematurely sort files/directories with --from*file.

Hanqin Guan (The OSLab of Peking University):
 - Fuzzing testing that identified several problems in --fromfile and
   --fromtabfile processing.
 - -U should disable --dirsfirst / --filesfirst sorting.
 - Make sure gittrim() function can handle a null string.

Trevor Gross
 - Suggested adding support for immediate values to -L with no spacing.

Christoph Anton Mitterer
 - Suggested option negation, which led to --opt-toggle.

Nicolai Dagestad
 - Suggested --hyperlink option.

Alchemyst (github)
 - Reported JSON error when using the --du option and unable to open a
   directory.
 - Reported that the reported total was incorrect when using the --du option.

Ivan Ivanovich
 - Reported small rounding error in human readable size output.

Kenta Arai
 - Reported Segfault with --filelimit option
 - Add NULL guard for json_printinfo() and xml_printinfo() (and fix ftype
   printing for XML)
 - Fix getcharset() to not return a getenv() pointer.
 - Suggest adding .gitignore file to the distribution.
 - Clean up some warnings issued by -Wextra

freemedom (github)
 - Provided Makefile information for cross compiling for Android.

David Seifert
 - Provided updates for Makefile
 - Suggested move to stdbool.h to avoid problems with newer compilers.

Daniel Li / Landon Bourma
 - Reported segfault from incorrect free.
 
And many others whom I've failed to keep track of.  I should have started
this list years ago.

					- Steve Baker
					  <EMAIL>
					  or
					  <EMAIL>
