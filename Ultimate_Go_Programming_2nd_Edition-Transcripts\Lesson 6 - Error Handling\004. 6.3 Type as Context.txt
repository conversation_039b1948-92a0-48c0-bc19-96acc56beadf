So, type as context applies when we need our own custom error type. Think of networking issues—how many problems can happen on the network? Just one? We all wish. There are tons of things that can go wrong, so a simple error variable isn’t enough to indicate there was a network issue. We need to know *what* the network issue was. The more detail we have about the error, the better we can determine whether we can recover from it or not. This is where type as context becomes very important when dealing with custom error types.

I want to show you two custom error types from the JSON package. The first is called `UnmarshalTypeError`. Notice it’s an unexported type with two exported fields: `Value` and `Type`. Also, look at line 22—this type implements the error interface using pointer semantics. Again, pointer semantics are the default, though there will be exceptions. I’ll come back to that. Pay attention to the implementation of the error interface itself. Remember, this implementation serves two purposes: it allows the concrete value to satisfy the error interface, and it’s used for logging. One thing I always look for is whether every field in the custom error type is included in the logging message. If a field is missing from the log output, I question its relevance. You created a custom error type and added a field because you thought it would help users, but it wasn’t important enough to include in the log? That could be a code smell.

We get this `UnmarshalTypeError` when you don’t pass a pointer into an `Unmarshal` call, because unmarshaling and decoding require pointer semantics—shared access. This error type is used specifically when there’s a type mismatch during JSON unmarshaling. For example, when the JSON document says a field is a string, but you’ve defined it as an integer in your concrete Go type. That’s exactly what this error is for, which is why it includes the `Value` and `Type` fields.

Now, here’s the second custom error type in the JSON package: `InvalidUnmarshalError`. This occurs when you don’t pass the address of a value into the `Unmarshal` call. Again, notice that the error interface is implemented using pointer semantics. Also, observe that the error message here is a bit more complex. So we have these two user-defined custom error types, both part of the standard library’s JSON package.

I want to show you the `Unmarshal` function. I’ve taken a code snippet from the JSON package to illustrate the `Unmarshal` call. The key thing to notice is the second parameter, which is based on the empty interface. The empty interface tells us nothing—any value or pointer satisfies it because it requires no behavior. We should be very careful when using the empty interface. Don’t use it to write generic APIs. Instead, use it when you need to pass data around that can be safely hidden, or in cases like this, when you’re going to use the reflect package to dynamically inspect the concrete data at runtime. This is great for model validation—reflect is fantastic for that, or for unmarshaling like we’re doing here.

Notice we use the reflect package’s `ValueOf` function. This is how reflection always starts. We’re essentially asking: is the concrete data inside `v` a pointer, and is it nil? More precisely, if it’s not a pointer or if it’s nil, we return this error value. Again, we implement the error interface with pointer semantics because we always need to share the error. So, we either store a pointer to an `InvalidUnmarshalError` in the error interface, or we share a `UnmarshalTypeError` pointer within it.

We’re processing from a decoupled state—`v interface{}`—but now the question is: what type of value is actually stored inside the error interface? What *type*? That’s where type as context comes in. Go provides a special mechanism for this, and it happens within a switch statement. Here’s how it works: we call `Unmarshal`, get back an error interface value, and ask: is there a concrete type stored inside? If so, we perform a type assertion using a type switch. Notice the keyword `type` is used in the switch—this is the only place in Go where you can use `type` in a type assertion within a switch.

This enables type as context. It says: perform a type assertion on what’s inside the error interface. If it’s a pointer to this concrete type, execute this case—and `e` will be a copy of that pointer. Or, if it’s a pointer to *this* type, execute that case, and `e` will be a copy of that pointer. Type as context is really powerful—the conditional logic depends on the concrete type stored, and thanks to the type assertion, we get a copy of the stored value so we can handle the error based on its concrete type.

But for me, this is also a problem. We want to maintain error handling in a decoupled state as much as possible. Once we shift from the decoupled interface to the concrete type, any improvements we make to error handling for those concrete types could cause cascading changes throughout the program. So even though type as context is super cool for error handling, it’s also kind of dangerous because it sets us up for potential widespread changes across the codebase.

However, type as context can be very powerful when you need to move concrete data across program boundaries and both sides need to work directly with the concrete data. In those cases, this mechanism is incredibly useful for transferring data while maintaining some level of decoupling.

I’m just cautious about using it in error handling. I’d prefer to process the error interface directly, or at least preserve as much decoupling as possible. So how can we maintain the benefits of custom error types without dropping into the concrete? That’s where we’ll start talking about *behavior as context*.