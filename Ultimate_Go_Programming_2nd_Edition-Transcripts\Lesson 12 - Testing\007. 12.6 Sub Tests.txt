Something that the language team added in Go 1.7 was the concept of subtests. <PERSON><PERSON>, who works for Google in the Switzerland office, did all of this work, and he did an amazing job with it. I want to show you subtests in Go. They really help when we start working with table-driven tests, because one of the benefits of table tests is the ability to do data-driven testing. But historically, if we wanted to filter out one piece of data and test just a single case, we had to comment out the other cases—meaning we had to make code changes. What subtests allow us to do is filter specific test cases directly at the command line. I’ll also show you how we can run these table tests in parallel.

Let’s start with our test download function. This is our standard test download function using the table-driven testing pattern we’ve seen before. But I’ve added a new field to the table: a name. This gives us the ability to name each test case, which then allows us to filter them on the command line. I’ve called one case the "status OK" test and the other the "status not found" subtest. These are effectively the same two test cases as before, but now each has a name.

Now, given the need to test downloading content, we range over our table. Here’s another change: I’ve defined a literal function and assigned it to the variable `tf`. Subtests work by binding each piece of data to its own literal function. That’s how we achieve filtering, execution, and parallelism. This literal function takes a pointer to `*testing.T`, and inside it, I define and run the rest of the test logic.

When we use a specific data input and its expected output, we perform the HTTP GET, close the response, and check the status. Now, instead of running the assertions directly in the loop, we have this literal test function `tf`. We range over the table, and here’s the key: we call `t.Run`. We pass `t.Run` the name of the subtest and the function to execute. Then we iterate to the next piece of data, and the next. 

How cool is this? I can now give each piece of data in my table test a name, define it within its own execution context—its own function—and then tell `t.Run` to execute each of these functions individually.

Let’s run the download test now. I’m already in the folder. Run `go test -run TestDownload -v` to get verbose output. You can see it found the subtests: "status OK" and "status not found." It executed both—first "status OK," then "status not found"—in series, one after the other. It took a full second to run.

But here’s the cool part: if I want to run only the "status OK" test, I can go back and specify `TestDownload/statusOK`. I don’t have to go into the code and comment out any lines. The testing tool now knows to run just that specific piece of data from the table test. This is really brilliant. Marcelle is a brilliant engineer at Google. We’re so lucky to have him on the team. He works on some powerful features, and this was something he spent a lot of time designing and building for us. It’s truly great, brilliant work.

Now, I want to show you how we can run all these test cases in parallel when it’s reasonable and practical to do so. I’ve added another test function called `TestParallelize`. It’s structured almost identically: here’s the table, here are the names, and here’s the data bound to those names. Given the need to test this, we range over the table and still define a literal function. We pass that function to `t.Run`, but look—this time, the very first thing I do inside the literal function is call `t.Parallel`.

This tells the testing framework: run this test as a separate, independent path of execution—literally, run it in its own goroutine. Don’t run it on the main test goroutine; run it on an independent one.

Now watch what happens when we run `TestParallelize`. Remember, the first version took a full second. Let’s run `go test -run TestParallelize -v`. Boom. Look how fast it ran—113 milliseconds, not a full second. Why? Because these tests ran in parallel, leveraging all the CPU cores available. This is really cool stuff—very cool.

Subtests are fantastic for data-driven tests because they allow us not only to isolate and run specific test cases from the command line but also to run all the cases in parallel. That means our unit tests can execute much faster in CI environments.