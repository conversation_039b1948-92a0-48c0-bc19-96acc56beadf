All right, now that we have that concrete implementation, let's go ahead and look at how we can decouple this code from change. One of the very first things we want to examine is the lower-level API. The lower-level API consists of pull and store. Our goal is to decouple pull from the concrete—nothing that pull knows about should be based on concrete types. It must be based on interfaces. To understand what interface we should use, I don’t have to guess. I already have an implementation of the lower-level API. Remember, I’ve implemented Xenia, and Xenia knows how to pull. I’ve implemented Pillar, and <PERSON><PERSON> knows how to store. My concrete implementation has already provided the behavior we need to decouple from the concrete. This is why I don’t want you to start with behavior or interfaces—we start with the concrete implementation.

Let’s go ahead and define those interfaces now. I’ll add two interfaces: the Puller interface, which knows how to pull, and the Storer interface, which knows how to store. I’m not guessing here—I already have a strong, working implementation of pull and store. We’ve worked this out, we’ve got a working program in production, and we know these APIs are good. We also know performance was sufficient; I didn’t have to worry about the primitive layer being too slow. It was fast enough—life is good. Now we can proceed with decoupling.

Let’s go into our lower-level API and replace Xenia with <PERSON>ull<PERSON>. Now we can use the Puller interface to execute the behavior. We’ve achieved the highest level of decoupling possible in Go—it’s thin and precise. We’re asking for any piece of concrete data—any value or pointer—that knows how to pull. We do the same for store: give me any piece of concrete data, any value or pointer, that knows how to store, and we do that through the API. All I’ve done is replace the lower-level API with a layer of decoupling. And now, boom—I can create other concrete types for other systems, and the lower-level API can work with them.

But we’re still not done. We need to decouple all the way through main. Right now, the only code that still depends on concrete types is main, specifically the Copy function. Copy is currently working with the concrete type System. It would be better to decouple Copy so it can work with any System that knows how to pull and store. Let’s go ahead and add a third interface. What’s powerful here is that we can leverage interface composition. Look at that—the PullStorer interface. It’s a common and idiomatic naming convention in Go: when we compose two interfaces, we name the resulting interface by combining their names, like ReadCloser or WriteCloser. Here, we have PullStorer.

Thanks to interface embedding, any piece of concrete data that knows how to pull and store is automatically a PullStorer. Isn’t System already a PullStorer, due to the composition of Xenia and Pillar? Absolutely. Our concrete type System already knows how to pull and store. Now look at what we can do: we can pull System out of the API. Another refactor—replace System with PullStorer. System already implements the PullStorer interface, which means that in main, when I pass a pointer to System to Copy, it already satisfies the PullStorer interface. Now, at least from the lower-level and higher-level API perspectives, the code is completely decoupled from change.

But there’s something in Copy that might concern you—it initially concerned me. Look at the calls to pull and store. You may not realize it, but we’re passing the PS variable into pull and into store. That should raise an alarm: “Whoa, whoa, whoa—Bill, pull isn’t looking for a PullStorer. Pull is looking for a Puller, and store is looking for a Storer. PS is not a Puller or a Storer. Why does this code compile?”

Let’s walk through it and I’ll explain. We start with System—it’s the embedding of Xenia and Pillar. Xenia knows how to pull, Pillar knows how to store, so System knows how to pull and store, thanks to Go’s interface promotion. We take the address of System when calling Copy. In Copy, remember, the function is completely decoupled—it takes a PullStorer interface. Interface values are valueless; interface types are valueless. They say: give me any concrete type—any value or pointer—that implements the PullStorer interface. We know System does.

So PS holds a pointer to System, and through pointer semantics, it stores the concrete System value. Now here’s the interesting part: we’re able to pass PS to pull and store because pull isn’t asking for a value of type Puller. There is no such thing as a “type Puller.” Pull is asking for any piece of concrete data that knows how to pull. Store is asking for any piece of concrete data that knows how to store. The key insight is this: we’re not passing PS to pull and store. PS is valueless—it doesn’t exist in the way you might think. What exists is the concrete data inside PS. When we call pull, we’re actually passing the concrete data stored inside PS—the System address. The same goes for store. We’re not passing PS; we’re passing the concrete data it contains.

So get this straight: we’re always moving and copying concrete data, whether that copy is done directly by you or indirectly through an interface. The interface itself is valueless—it only gains meaning when it holds a concrete value. We’re always moving the concrete type across boundaries. In this case, the data being moved is the address of System. That’s why this works. You could call this implicit interface conversion, but I’d rather emphasize that it’s the concrete data being moved, not the interface value. The interface value is just a vessel.

We’re still not done, because I said we need to decouple all the way through main. Right now, if I want to use Alice or Bob instead of Xenia and Pillar, I can’t. We have a concrete type called System, which is the embedding of Xenia and Pillar. To use Alice or Bob, I’d either have to break System—which is bad—or create another system type, like System2. But think about this: if I do that, it’s not scalable. System2 is a PullStorer, and yes, it would work. But what about all the possible combinations of Xenia, Pillar, Bob, Alice, and others? This approach doesn’t scale. It’s not the direction we want to go.

So how do we solve this? Let me ask you a question: what if System wasn’t the embedding of Xenia and Pillar, but instead the embedding of the two interfaces—Puller and Storer? Think about it. I’ve just defined a concrete type where I can inject other concrete data based on behavior. Now I only need one System type, because I can inject different concrete implementations into it. How do we make this change?

Let’s go in and redefine System: now it embeds Puller and Storer. We need to use pointer semantics because we implemented the interfaces with pointer receivers. Now look: we’re injecting Xenia and Pillar as the concrete implementations. Later, we can inject Bob or Alice if we want—all through this one concrete type. We can still pass System to Copy, and everything should work. There it is. We’re now able to inject data into our concrete type and achieve full decoupling. We can define a system to be almost anything, as long as the injected data implements the required behavior.

After we get code working, it’s critical to do a code readability review. These reviews are essential. There are really two types of code reviews you should do. The first is the readability review, which you should perform every time you get code working. Its purpose is to ensure consistency in variable names, API design, and semantics. These things must be in place. Treat the readability review as a way to validate your mental model of every function, method, structure, naming, and organization.

The second review is the technical review, where you look at algorithm efficiency, API improvements, and other optimizations. But the readability review comes first and is critical.

When I did a readability review here, I realized something: I’m never going to have more than one System type. Thanks to interface embedding, System is already fully decoupled. Since I won’t have multiple implementations of PullStorer, that interface is now pollution. I can go back to working directly with the concrete type because I don’t need the abstraction. Having the interface is a waste of time. I can remove it and use the concrete type instead. I can change the higher-level API back to accepting System directly. This code still works, and I’m still at the highest level of decoupling possible, thanks to data injection.

I did another readability review and realized something else: data injection is cool, but it’s clever—maybe too clever. Because this API isn’t as precise as it could be. Honestly, wouldn’t this be a more precise API? Think about it. The System type hides the cost of initialization. When someone new looks at Copy, they don’t immediately see that it needs pull and store behaviors. But with this new API, we reduce confusion. We make the function easier to use and easier to test because it explicitly says: give me concrete data that implements Puller, and give me concrete data that implements Storer. Now we know exactly what Copy needs and what it does—much clearer than using a System abstraction.

I want to be as precise as possible. So if we update our API to take Puller and Storer directly, we pass the concrete implementations—Xenia and Pillar—into Copy. That solves the problem. Even main cleans up. Now we can just construct the concrete types directly. The concept of the interface disappears from initialization. Let’s clean this up and remove the unnecessary abstraction. We’ll use Go’s zero-value initialization to simplify.

Now we share Xenia and Pillar directly with Copy. Even our main program is much cleaner because we’re focused on what matters: the concrete. During initialization, we focus on the concrete data—Xenia and Pillar are the problem. We pass Xenia and Pillar into Copy, a higher-level API. That API is already decoupled: it asks for anything that implements Puller and anything that implements Storer. Now we have decoupling throughout the entire application.

We’ve built an API that minimizes misuse and reduces the potential for errors. Notice that this was a refactoring process—and I don’t want you to be afraid of refactoring. If you’re not refactoring, you’re not improving. If you’re not refactoring, you’re probably doing it wrong.

Look at the approach we took: layering the API, validating each layer was testable, thinking about tests from a data perspective—not from a decoupling perspective. I don’t want to hear, “Oh Bill, we’ve got to write tests, so we’ve got to decouple the code—let’s throw an interface at it.” No, no, no. Instead: layer, test through data, build incrementally, implement a concrete solution first, then ask what needs to be decoupled. Decouple through layers of refactoring. Then do code readability reviews to tighten up the APIs. Ask: where is there potential for misuse? Where can we be more precise now that we understand the problem better? Then make those changes.