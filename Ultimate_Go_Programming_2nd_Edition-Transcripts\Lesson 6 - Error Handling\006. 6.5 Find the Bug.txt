I want to show you some more of the interface mechanics here through this code. This is a really interesting piece of code because our mechanics or our semantics might trick us. On the other hand, if we have a strong understanding of mechanics and semantics, we can truly understand the behavior of this code. This code is based on a bug that a friend of mine—a very experienced Go developer—produced a couple of years ago. As soon as I saw it, I told him, "We're putting this in the training." Take a look at this code. Let's see if you can find the bug.

On line 10, I define a custom error type. Notice the naming convention again—it ends in "error." We're using an empty struct as the base because we don't need any state, and I implement the error interface using pointer semantics, which is the correct approach for struct types. Find the bug.

I've written a function called *fail*, and I want you to notice that *fail* does not fail. The function returns *nil* for the error in the second return parameter. What happens next? On line 23, I declare an error interface variable initialized to its zero value—a nil interface. We call *fail*, which does not fail, and assign the returned result back to our local error variable. Even though *fail* does not fail, we check whether it failed, and guess what? According to this code, *fail* did fail.

How is it possible that *fail* is failing when we're returning *nil*—the zero value—for the error? I want you to think about this for a few seconds. We don't have time to sit here all day, but take a moment. Why is *fail* failing?

Because there's a fundamental bug in this code that everyone makes at some point. I made it myself early on in Go. Let's walk through the code to see what's happening.

On line 23, we create an error interface value. It's an interface variable set to its zero value—nil. There is no concrete data stored inside this interface. When we call *fail*, it returns a value of *nil*. And yes, the value we're storing in the interface is indeed *nil*. That hasn't changed. But the critical question is: *What type of nil is it?*

Remember, *nil* takes on the type it needs. If you look at the function signature, the return type is a pointer to a custom error. So what we're actually returning is a *nil pointer of type *customError*. That means we're storing a concrete type—*customError*—inside the interface, even though the value is *nil*. 

As far as the interface is concerned, it now contains a concrete type with a *nil* value. The interface itself is not *nil*—it holds a typed *nil*. Therefore, when we check `if err != nil`, the condition evaluates to true because the interface is not empty; it contains a type, even if the value is *nil*. This is the bug.

The developer didn't return the error interface value—they returned the custom error type. Remember, the custom error type is a concrete type, an artifact. Error handling in Go happens in a decoupled state, which means we must always return the interface value, not the concrete type.

To fix this code, we need to return the *nil* interface value, not a *nil* pointer to a concrete type. That way, the interface remains completely empty—no type, no value—and the nil check will work as expected.

Let me share one more thing with you. I've been emphasizing that we should use pointer semantics when implementing the error interface, unless the underlying data is a reference type like a slice—something that already uses value semantics in practice. But when dealing with struct types, I strongly recommend using pointer semantics, not value semantics.

Let me show you why. This next example will teach us the final piece of interface mechanics, just like the previous one revealed important behavior.

Let's go back to the original code where we have a web call that can return an error like *badRequest*. Suppose I decide to get clever and create my own error variable called *errBuild*. There it is—I'm mimicking the error that comes out of the web call. Great.

Now, instead of checking if the error is not *nil*, I decide to change my error handling logic to check if the error is equal to *errBuild*: `if err == errBuild`. 

Now, the question is: should *err* equal *errBuild*? Well, *errBuild* is created with `new(customError)`—it's a new value, a new address. When I run this, as expected, *err* is not equal to *errBuild*. That makes sense because we're calling `new`, which returns a new pointer each time.

But remember—we're using pointer semantics. Now, let's change the implementation to use value semantics—the thing I've been telling you not to do with struct types. I'm going to use value semantics for the implementation of the `Error()` method.

So I change the receiver from `*customError` to `customError`. Now, when I store this in the interface, I'm storing a value, not a pointer. And now, when I run the code—what happens?

Whoa. Suddenly, *err* equals *errBuild*. They are now considered equal.

Why?

I'm going to say this one last time: interface values are not real. Interface values are valueless. There is an implementation detail, but from our programming model, interfaces themselves have no intrinsic value. The only thing that's real is the concrete data stored inside them.

Therefore, when we compare two interface values, we are not comparing the interfaces themselves—because that's impossible; they're not real. We are comparing the concrete data stored inside them. That's the only thing we *can* compare. That's the only thing that's real.

Now that I'm using value semantics, think about what's happening. The *err* variable contains a *customError* value—say, with the string "bad request." The *errBuild* variable also contains a *customError* value with the same string. Since both interfaces are storing values of the same type, Go compares the values directly.

And yes, the values are the same—they contain the same string. So the comparison returns true. But this is dangerous. We're essentially doing a string comparison, which can lead to false positives and fragile code.

Now, why doesn't this happen when we use pointer semantics?

When we use pointer semantics, the interfaces store pointers—addresses—not values. So *err* stores the address of one *customError* instance, and *errBuild* stores the address of another. When we compare them, we're comparing addresses, not values. And the address of one instance can never be the same as the address of another. So the comparison correctly returns false.

The mechanic here is this: interface values are not real. They are valueless. The only thing real is the concrete data stored inside them. When we compare interface values, we are comparing the concrete data—not the interfaces.

And how that data is stored—whether by value or by pointer—determines the comparison behavior. With value semantics, we compare the actual values. With pointer semantics, we compare the addresses.

This is the same principle that applies when passing interface values across program boundaries. We're not passing the interface value itself—that's valueless, not real. We're passing the concrete data stored inside it. And how we store that data—by value or by pointer—determines everything.

These are crucial mechanics. They help you read code, understand behavior, and predict impact. Again, our goal is to look at a piece of code, understand its semantics, understand its behavior, and then understand its real-world impact.

We're going to use interfaces extensively in Go. We don't want interface pollution, but decoupling will be a big part of your daily work. Having a strong grasp of these mechanics and semantics will make you a better Go developer.