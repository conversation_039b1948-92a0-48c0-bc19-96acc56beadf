Welcome to Lesson 12: Testing. This is where we're going to start learning how the testing tool works and explore the testing framework already built into the language. It's beautiful that Go provides this out of the box. Unit testing, and even some integration testing, is so important. I'm going to teach you how to write unit tests, sub-tests, and data-driven tests. I'll also show you how to mock HTTP-related functionality in the language, as well as how to write documentation and examples. This is a great lesson to get your feet wet and start writing unit tests in Go.