Welcome to lesson 14, Profiling and Tracing—probably one of the most fun lessons in the entire training. I want to stress this again: please, I'm practically praying that you've gone through everything else before arriving here, because I'm going to dive into some lower-level implementation details. I'll do my best to show you how these tools work in a practical way. What's brilliant is that I've built this entire lesson using Go 1.11 beta one. By the time you see this, you'll likely already be on Go 1.11 or even 1.12, which gives us a real advantage in exploring the serious and significant improvements that have been made to profiling and tracing. I'm extremely excited to show you just how powerful Go's tooling is in this area and why I believe it sets Go apart from many other programming languages.