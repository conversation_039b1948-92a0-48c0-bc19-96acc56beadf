Welcome to lesson nine, data races. This is where we start learning important topics around synchronization and orchestration, though we'll primarily focus on synchronization. As a multi-threaded software developer, these are the two things you must worry about: synchronization and orchestration. Data races represent some of the nastiest bugs you'll ever encounter. A data race occurs when at least two goroutines access the same memory location concurrently, with one performing a read and the other a write. This is bad. In this lesson, I'll demonstrate data races, show you how to use the race detector tool, and teach you how to fix data races using atomics and mutexes. We'll also deepen our understanding of mechanical sympathies—how the underlying hardware works.