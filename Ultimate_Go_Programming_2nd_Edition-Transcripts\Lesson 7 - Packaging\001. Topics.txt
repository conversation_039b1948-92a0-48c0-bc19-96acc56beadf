Welcome to lesson seven, Packaging. This may be one of the most important lessons when it comes to sitting down and starting to write production code. How do you structure projects? How do you think about packaging APIs? What are the relationships between packages? What are the costs of coupling? What is the cost of not decoupling? All of these topics will be covered here. I'm going to give you both the mechanics and the design philosophies, and I'll share how I apply these principles on a day-to-day basis in our projects for clients, along with the success we're seeing with package-oriented design. This is where we begin to truly learn how to sit down, structure Go projects, think about packaging APIs, and work effectively as a team moving forward.