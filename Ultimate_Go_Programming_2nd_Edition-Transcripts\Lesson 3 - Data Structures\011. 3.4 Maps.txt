Alright, we're going to look at that third data structure that Go gives us. Remember we had arrays, we just went through slices, and now we're going to talk about maps. Maps are simply key-value pairs. There's a hashing algorithm underneath— you can't play with it, you can't touch it. But like everything in Go, it's about conventional reconfiguration. The Go team has done a great job with all of the algorithms to make them really general-purpose and effective for most, if not all, of the use cases you're going to encounter.

Now let's take a look at some mapping code. Here's a basic example. We have a user-defined type called `user` on line 11. Then we're using the built-in function `make`— remember, `make` allows us to create slices, maps, and we'll see it again later with channels. Here, we're making a map. A map is not usable in its zero value state, unlike a slice. A map must be constructed either with `make` or with a literal form, which we'll look at in a moment. Once you make the map, you can proceed with key-value initialization as shown here.

In this case, the map has string keys and stores values of type `user`. We're using standard assignment syntax to insert `user` values into the map. Importantly, we're using value semantics here—the map receives a copy of the `user` value, not a pointer. That means when we retrieve values, we're getting copies. This is value semantics all the way through.

Once we've populated the map with some values, we can iterate over it using `for range`. Previously, when using `for range` on slices or arrays, we got the index and a copy of the value when using value semantics. With maps, we get the key and the value—again, both are copies. So remember, we're not operating directly on the `user` value stored in the map; we're working with a copy.

When we range over the map, there's an important detail: iteration order is random. Go intentionally randomizes map iteration order, and this randomness has been improved over the years. The reason is to prevent developers from writing code that assumes a particular ordering based on how keys were inserted. You must recognize that map iteration is inherently random. If I run this code, on the first iteration I might see Jackson, Roy, Ford, Mouse; on the second, a different order like Roy, Ford, Mouse, Jackson. So understand that when you iterate over a map, the order will vary each time you run it.

There are other interesting aspects of maps, including the ability to use literal construction. Here's the `user` type again. Instead of using `make` followed by assignments, I'm now using literal construction. We've seen this with structs, slices, and arrays—here it is again with maps. This syntax constructs the map and makes it ready for use. Inside the literal, we define key-value pairs using a colon, and we include a trailing comma.

I can iterate over this data just as before. But notice there's also a built-in function called `delete`, which allows you to remove a key from the map. This is important because of potential memory leaks. I've seen cases where memory grows out of control because people use maps as caches but never remove keys. At some point, if you're using a map as a cache, you must remove keys—whether based on time, size, or some application event.

Once I remove the key "Roy"—which was the first value inserted—and then try to retrieve it, I get a zero value. Look at the syntax: I can retrieve data from a map and just get the value. If the key doesn't exist, the value returned is the zero value of the type. But that doesn't necessarily mean the key wasn't present—you might have intentionally stored a zero value for that key.

That's why the second form of retrieval, which includes a boolean indicating whether the key was found, is what you should use to verify key existence. If I run this program, you'll see the data, then I remove the key "Roy" on line 32, and when I try to retrieve it, the `found` variable is false. So we have `delete` to remove keys—we need to use it because we can't store data forever. And we have two forms of retrieval: one that returns just the value, and one that also returns a boolean to confirm presence.

There are restrictions on what can be a key—but not on values. You can store anything as a value in a map, thanks to the empty interface. But keys must be hashable. Always think of a key as something you can use in an `if` statement—something you can compare. If you can compare two values, you can use that type as a key.

For example, on line 17 I define a type called `users`, which is a slice of `user`. A slice of `user` isn't comparable, so if I try to use `users` as a key, I get a compiler error on line 24: "invalid key type." So remember: not everything can be a key, but everything can be a value. If you can't use a piece of data in a comparison, it's likely not a valid key.

Now let me show you a few more things. Go introduced a new package called `sort`, and I want to show how we can use it to sort a map's keys for more predictable iteration. Here's the `user` type again, and we're storing data in a map. Even though we insert in a certain order, iteration remains random.

Here's what we do: we define a slice of strings to hold the keys. We range over the map—randomly, since order isn't guaranteed—and append each key to the slice. Then we use the `sort` package: specifically, `sort.Strings` to sort the keys alphabetically. Now I have an ordered list of keys.

On line 38, when I range over this sorted slice of keys, I'm iterating in alphabetical order. For each key, I perform a lookup in the map. This way, the output is predictable and sorted. The new `sort` package is powerful and gives us new ways to bring order to otherwise random map iteration.

Maps are a fundamental data structure in Go. You'll use them whenever you need key-value pairs or want to index into data. While slices should be your first choice—because they offer mechanical sympathy through predictable access patterns—maps also try to keep data contiguous, so there's some mechanical sympathy there too.

But think of the map as the go-to data structure when you need to quickly locate data using a key. It's efficient, flexible, and essential for many programming tasks in Go.